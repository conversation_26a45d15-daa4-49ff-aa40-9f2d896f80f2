'use client';

import React, { useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuth } from '../../../components/auth/AuthProvider';
import UnifiedSentenceCategorySelector, { SentenceSelectionConfig } from '../../../components/games/UnifiedSentenceCategorySelector';

export default function SentenceBuilderPage() {
  const { user } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();
  const assignmentId = searchParams?.get('assignment');

  // Game state management
  const [gameStarted, setGameStarted] = useState(false);
  const [selectedConfig, setSelectedConfig] = useState<SentenceSelectionConfig | null>(null);

  // Handle selection complete from sentence selector
  const handleSelectionComplete = (config: SentenceSelectionConfig) => {
    setSelectedConfig(config);
    setGameStarted(true);
    console.log('Sentence Builder started with sentence config:', config);
  };

  // Handle back to menu
  const handleBackToMenu = () => {
    setGameStarted(false);
    setSelectedConfig(null);
  };

  // Show sentence category selector if game not started
  if (!gameStarted) {
    return (
      <UnifiedSentenceCategorySelector
        gameName="Sentence Builder"
        title="Sentence Builder - Select Content"
        supportedLanguages={['spanish', 'french', 'german']}
        showCustomMode={true}
        onSelectionComplete={handleSelectionComplete}
        onBack={() => router.push('/games')}
      />
    );
  }

  // Placeholder game implementation
  if (gameStarted && selectedConfig) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-400 via-pink-500 to-red-500 flex items-center justify-center">
        <div className="text-center text-white">
          <h2 className="text-2xl font-bold mb-4">Sentence Builder</h2>
          <p className="mb-4">Game integration in progress...</p>
          <p className="mb-4 text-sm">
            Selected: {selectedConfig.language} - {selectedConfig.curriculumLevel} - {selectedConfig.categoryId}
            {selectedConfig.subcategoryId && ` - ${selectedConfig.subcategoryId}`}
          </p>
          <button
            onClick={handleBackToMenu}
            className="bg-white/20 hover:bg-white/30 px-6 py-3 rounded-lg transition-colors"
          >
            Back to Selection
          </button>
        </div>
      </div>
    );
  }

  return null;
}
